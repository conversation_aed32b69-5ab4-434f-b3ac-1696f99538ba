<context>
# Overview  
Triển khai ONE_TO_ONE relationship type trong Twenty CRM để hỗ trợ các mối quan hệ một-đối-một giữa các objects. Hiện tại hệ thống chỉ hỗ trợ MANY_TO_ONE và ONE_TO_MANY relationships. Việc thêm ONE_TO_ONE sẽ cho phép mô hình hóa các mối quan hệ như User-Profile, Company-CompanySettings, hoặc Contact-ContactDetails một cách chính xác hơn.

Tính năng này sẽ giải quyết vấn đề không thể tạo ra các mối quan hệ độc quyền giữa các records, và cung cấp khả năng data modeling hoàn chỉnh hơn cho người dùng.

# Core Features  
1. **ONE_TO_ONE RelationType Support**
   - Thêm ONE_TO_ONE vào RelationType enum ở cả frontend và backend
   - Đ<PERSON>m bảo tính nhất quán với Chrome Extension (đã có sẵn)
   - Validation để đảm bảo mỗi record chỉ có thể link với tối đa 1 record khác

2. **Database Schema Updates**
   - Cập nhật migration để support ONE_TO_ONE constraints
   - Thêm unique constraints khi cần thiết
   - Đảm bảo referential integrity

3. **UI Components for ONE_TO_ONE**
   - Sử dụng các icon đã có sẵn (IllustrationIconOneToOne, IconRelationOneToOne)
   - Tạo input/display components riêng cho ONE_TO_ONE
   - Cập nhật field creation wizard

4. **GraphQL Schema Updates**
   - Cập nhật generated types
   - Thêm resolvers cho ONE_TO_ONE operations
   - Update mutations và queries

# User Experience  
**User Personas:**
- Data Administrators: Cần tạo ra relationship structure chính xác
- End Users: Cần hiểu và sử dụng ONE_TO_ONE relationships một cách trực quan

**Key User Flows:**
1. **Creating ONE_TO_ONE Relationship:**
   - User vào Settings > Data Model
   - Chọn object và tạo new field
   - Chọn "Relation" type và "One to One" option
   - Configure target object và field names
   - System tạo ra reverse field tự động với ONE_TO_ONE constraint

2. **Using ONE_TO_ONE in Records:**
   - User thấy relation field như một select/autocomplete
   - Chỉ có thể chọn 1 record và record đó chưa được link
   - Khi link, system prevent record khác link với cùng target
   - Clear visual indication về ONE_TO_ONE constraint

**UI/UX Considerations:**
- Icon và illustration phải khác biệt rõ ràng với ONE_TO_MANY
- Error messaging rõ ràng khi vi phạm constraint
- Intuitive selection experience với filtering
</context>

<PRD>
# Technical Architecture  

**System Components:**
1. **Backend (twenty-server):**
   - RelationType enum update trong field-metadata module
   - Field creation validation service
   - Database relationship management
   - GraphQL schema generation

2. **Frontend (twenty-front):**
   - RelationType constant updates
   - New ONE_TO_ONE input/display components
   - Field settings UI updates
   - Data model management interface

3. **Shared (twenty-shared):**
   - Type definitions synchronization
   - Utility functions for relation validation

**Data Models:**
- FieldMetadataType.RELATION với RelationType.ONE_TO_ONE
- Unique constraints trên foreign key columns
- Bidirectional relationship metadata

**APIs and Integrations:**
- Update GraphQL mutations cho field creation
- Relation validation endpoints
- Data integrity checking APIs

**Infrastructure Requirements:**
- Database migration scripts
- Schema regeneration tools
- Backward compatibility maintenance

# Development Roadmap  

**Phase 1: Backend Foundation (MVP)**
- Cập nhật RelationType enum trong server
- Thêm ONE_TO_ONE validation logic trong field-metadata.service.ts
- Update database relationship creation logic
- Thêm constraints và validation rules

**Phase 2: Frontend Basic Support**
- Update RelationType constants trong frontend
- Tạo basic ONE_TO_ONE input component
- Update field creation wizard để support ONE_TO_ONE
- Basic display component cho ONE_TO_ONE fields

**Phase 3: Complete UI Experience**
- Polish UI components với proper icons
- Advanced validation và error handling
- Comprehensive testing
- Data model management interface updates

**Phase 4: Advanced Features & Polish**
- Migration tools cho existing data
- Performance optimizations
- Advanced constraint options
- Documentation và user guides

# Logical Dependency Chain

**Foundation First (Critical Path):**
1. **Server-side RelationType update** - Cơ sở cho mọi thứ khác
2. **Database constraint logic** - Đảm bảo data integrity
3. **GraphQL schema regeneration** - API contract updates

**Frontend Development:**
4. **Frontend RelationType constants** - Cần sau server update
5. **Basic ONE_TO_ONE components** - UI building blocks
6. **Field creation UI** - User-facing interface

**Integration & Polish:**
7. **End-to-end testing** - Đảm bảo flow hoạt động
8. **Error handling & validation** - Production-ready experience
9. **Documentation & migration guides** - User adoption

**Getting to Usable Frontend Quickly:**
- Phase 1 + Phase 2 sẽ cho ra MVP có thể demo được
- Priority: Basic field creation và display trước advanced features
- Focus vào happy path trước edge cases

# Risks and Mitigations  

**Technical Challenges:**
- **Risk:** Existing data có thể conflict với ONE_TO_ONE constraints
- **Mitigation:** Tạo migration scripts với data validation và cleanup options

- **Risk:** GraphQL schema changes có thể break existing queries
- **Mitigation:** Backward compatibility checks và incremental rollout

**MVP Definition:**
- **Risk:** Scope creep với advanced constraint options
- **Mitigation:** Focus vào basic ONE_TO_ONE implementation trước, advanced features sau

**Resource Constraints:**
- **Risk:** Multiple components cần update đồng thời
- **Mitigation:** Phased approach với clear dependencies, test từng phase riêng biệt

**Data Integrity:**
- **Risk:** Existing relationships có thể violate ONE_TO_ONE constraints
- **Mitigation:** Analysis tools để check existing data trước khi enable

# Appendix  

**Research Findings:**
- Chrome Extension đã có đầy đủ 4 relation types (MANY_TO_MANY, MANY_TO_ONE, ONE_TO_MANY, ONE_TO_ONE)
- UI assets đã sẵn sàng (IllustrationIconOneToOne, IconRelationOneToOne)
- Server hiện tại chỉ hỗ trợ MANY_TO_ONE và ONE_TO_MANY trong validation logic

**Technical Specifications:**
- RelationType enum cần sync giữa frontend và backend
- Database unique constraints cần careful consideration
- GraphQL generated types sẽ tự động update sau enum changes

**Files cần modify chính:**
- packages/twenty-server/src/engine/metadata-modules/field-metadata/interfaces/relation-type.interface.ts
- packages/twenty-front/src/modules/settings/data-model/constants/RelationTypes.ts
- packages/twenty-server/src/engine/metadata-modules/field-metadata/field-metadata.service.ts
- UI components trong twenty-front/src/modules/object-record/record-field/
</PRD> 