**/**/.env
.DS_Store
/.idea
**/**/node_modules/
.cache

# yarn is the recommended package manager across the project
**/**/.package-lock.json

.nx/installation
.nx/cache

.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions
.vercel
.swc

**/**/logs/**

coverage
dist
storybook-static
*.tsbuildinfo
.eslintcache
.nyc_output
test-results/
dump.rdb
.tinyb

.notes
/data/
/.devenv/
/.direnv/
/.pre-commit-config.yaml
/.envrc
/devenv.nix
/flake.lock
/flake.nix

.crowdin.yml
.react-email/

mcp.json

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log
# Dependency directories
node_modules/
# Environment variables
.env
# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific

# Task files
# tasks.json
# tasks/ 